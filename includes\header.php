<?php include_once 'config.php'; ?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle() . ' – ' . SITE_TITLE; ?></title>
    <meta name="description" content="Türkiye'nin en iyi atletizm pisti yapan firması. IAAF onaylı atletizm pisti yapımı, profesyonel hizmet ve anahtar teslim çözümler.">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="index.php">
                        <img src="assets/images/logo.png" alt="Atletizm Pisti Logo" class="logo-img">
                    </a>
                </div>
                
                <nav class="navigation">
                    <ul class="nav-menu">
                        <?php foreach ($navigation as $title => $link): ?>
                            <li class="nav-item <?php echo is_array($link) ? 'has-dropdown' : ''; ?>">
                                <?php if (is_array($link)): ?>
                                    <a href="#" class="nav-link"><?php echo $title; ?></a>
                                    <ul class="dropdown-menu">
                                        <?php foreach ($link as $sub_title => $sub_link): ?>
                                            <li><a href="<?php echo $sub_link; ?>"><?php echo $sub_title; ?></a></li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php else: ?>
                                    <a href="<?php echo $link; ?>" class="nav-link <?php echo isActivePage($link) ? 'active' : ''; ?>">
                                        <?php echo $title; ?>
                                    </a>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </nav>

                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <nav class="mobile-navigation">
        <div class="mobile-nav-content">
            <div class="mobile-logo">
                <a href="index.php">
                    <img src="assets/images/logo.png" alt="Atletizm Pisti Logo">
                </a>
            </div>
            <ul class="mobile-nav-menu">
                <?php foreach ($navigation as $title => $link): ?>
                    <li class="mobile-nav-item <?php echo is_array($link) ? 'has-dropdown' : ''; ?>">
                        <?php if (is_array($link)): ?>
                            <a href="#" class="mobile-nav-link"><?php echo $title; ?></a>
                            <ul class="mobile-dropdown-menu">
                                <?php foreach ($link as $sub_title => $sub_link): ?>
                                    <li><a href="<?php echo $sub_link; ?>"><?php echo $sub_title; ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <a href="<?php echo $link; ?>" class="mobile-nav-link <?php echo isActivePage($link) ? 'active' : ''; ?>">
                                <?php echo $title; ?>
                            </a>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </nav>

    <main class="main-content">
