<?php
// Site Configuration
define('SITE_TITLE', 'Türkiye\'nin En İyi Atletizm Pisti Yapan Firması');
define('SITE_URL', 'http://localhost');
define('COMPANY_NAME', 'Bilgili İnşaat');
define('COMPANY_PHONE', '0216 650 83 34');
define('COMPANY_WHATSAPP', '905335041020');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_ADDRESS', 'Acıbadem, Gayretli Sokağı No:14 D:7, 34660 Üsküdar/İstanbul');

// Navigation Menu
$navigation = [
    'Ana Sayfa' => 'index.php',
    'Hakkımızda' => 'hakkimizda.php',
    'Bilgiler' => [
        'Atletizm Pisti Nedir ?' => 'bilgiler/atletizm-pisti-nedir.php',
        'Atletizm Pisti Nasıl Yapılır ?' => 'bilgiler/atletizm-pisti-nasil-yapilir.php',
        'Atletizm Pisti Yapımı ve Ölçüleri' => 'bilgiler/atletizm-pisti-yapimi.php',
        'Atletizm Pisti Yapımı Fiyatları' => 'bilgiler/atletizm-pisti-yapimi-fiyatlari.php',
        '1000 Kişilik Atletizm Pisti' => 'bilgiler/1000-kisilik-atletizm-pisti.php',
        '2000 Kişilik Atletizm Pisti' => 'bilgiler/2000-kisilik-atletizm-pisti.php',
        '5000 Kişilik Atletizm Pisti' => 'bilgiler/5000-kisilik-atletizm-pisti.php',
        '10000 Kişilik Atletizm Pisti' => 'bilgiler/10000-kisilik-atletizm-pisti.php',
        '20000 Kişilik Atletizm Pisti' => 'bilgiler/20000-kisilik-atletizm-pisti.php',
        '50000 Kişilik Atletizm Pisti' => 'bilgiler/50000-kisilik-atletizm-pisti.php',
        'IAAF Onaylı Atletizm Pisti Yapımı' => 'bilgiler/iaaf-onayli-atletizm-pisti-yapimi.php',
        'IAAF Onaylı Atletizm Pisti Yapımı ve Maliyeti, Ölçüleri' => 'bilgiler/iaaf-onayli-atletizm-pisti-yapimi-ve-maliyeti-olculeri.php',
        'IAAF Onaylı Tartan Atletizm Pisti Yapan Firma' => 'bilgiler/iaaf-onayli-tartan-atletizm-pisti-yapan-firma.php'
    ],
    'Referanslar' => 'referanslar.php',
    'İletişim' => 'iletisim.php'
];

// Page titles
$page_titles = [
    'index.php' => 'Atletizm Pisti',
    'hakkimizda.php' => 'Hakkımızda',
    'iletisim.php' => 'İletişim',
    'referanslar.php' => 'Referanslar'
];

// Helper function to get current page
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF']);
}

// Helper function to get page title
function getPageTitle() {
    global $page_titles;
    $current_page = getCurrentPage();
    return isset($page_titles[$current_page]) ? $page_titles[$current_page] : 'Atletizm Pisti';
}

// Helper function to check if current page is active
function isActivePage($page) {
    return getCurrentPage() === $page;
}
?>
