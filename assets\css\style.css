/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo img {
    height: 60px;
    width: auto;
}

.navigation .nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #e74c3c;
}

.has-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #fff;
    min-width: 300px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border-radius: 8px;
    padding: 1rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.5rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown-menu a:hover {
    background-color: #f8f9fa;
    color: #e74c3c;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    transition: all 0.3s ease;
}

.mobile-navigation {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    z-index: 1002;
}

.mobile-nav-content {
    background: #fff;
    width: 300px;
    height: 100%;
    padding: 2rem;
    overflow-y: auto;
}

.mobile-nav-menu {
    list-style: none;
    margin-top: 2rem;
}

.mobile-nav-item {
    margin-bottom: 1rem;
}

.mobile-nav-link {
    display: block;
    padding: 0.75rem 0;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid #eee;
}

/* Main Content */
.main-content {
    margin-top: 80px;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
}

.btn-primary {
    background: #e74c3c;
    color: white;
}

.btn-primary:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #333;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Section Styles */
section {
    padding: 4rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.section-header h3 {
    font-size: 1.5rem;
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #7f8c8d;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    background: #f8f9fa;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-content p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #555;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
}

.feature-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.feature-card p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* Quick Features */
.quick-features {
    background: #2c3e50;
    color: white;
}

.quick-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.quick-feature {
    text-align: center;
    padding: 1.5rem;
}

.quick-feature h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #ecf0f1;
}

.quick-feature p {
    color: #bdc3c7;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.footer a {
    color: #3498db;
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* Fixed Contact Buttons */
.fixed-contact-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.whatsapp-btn,
.phone-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.whatsapp-btn {
    background: #25d366;
    color: white;
}

.phone-btn {
    background: #3498db;
    color: white;
}

.whatsapp-btn:hover,
.phone-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navigation {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .quick-features-grid {
        grid-template-columns: 1fr;
    }

    .fixed-contact-buttons {
        bottom: 10px;
        right: 10px;
    }

    .container {
        padding: 0 15px;
    }
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

/* Company Info Section */
.company-info {
    background: #f8f9fa;
    padding: 4rem 0;
}

.company-info-content {
    max-width: 900px;
    margin: 0 auto;
}

.company-image {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 12px;
    margin: 0 auto 2rem;
    display: block;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.company-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #555;
    text-align: justify;
}

/* Gallery Section */
.gallery-section {
    padding: 4rem 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Success Section */
.success-section {
    background: #f8f9fa;
    padding: 4rem 0;
}

.success-content {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
}

.success-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.success-content > p {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin-bottom: 3rem;
}

.success-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.success-feature {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: left;
}

.success-feature h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.success-feature p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

/* About Page Styles */
.about-page-content {
    padding: 4rem 0;
}

.about-text {
    max-width: 800px;
    margin: 0 auto 4rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #555;
    text-align: justify;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.about-feature {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.feature-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #e74c3c;
    min-width: 80px;
}

.feature-text h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.feature-text p {
    color: #7f8c8d;
}

.company-values {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 12px;
}

.company-values h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.value-item {
    text-align: center;
    padding: 1.5rem;
}

.value-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.value-item p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* Contact Page Styles */
.contact-section {
    padding: 4rem 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
}

.contact-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.contact-item p {
    color: #555;
    line-height: 1.6;
}

.contact-item a {
    color: #e74c3c;
    text-decoration: none;
}

.contact-item a:hover {
    text-decoration: underline;
}

/* Contact Form */
.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
}

.form-group small {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.success-message,
.error-message {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
}

.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* References Page */
.references-section {
    padding: 4rem 0;
}

.references-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.references-intro p {
    font-size: 1.1rem;
    color: #7f8c8d;
    max-width: 600px;
    margin: 0 auto;
}

.references-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.reference-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.reference-item:hover {
    transform: translateY(-5px);
}

.reference-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.reference-info {
    padding: 1.5rem;
}

.reference-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.reference-info p {
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.reference-year {
    background: #e74c3c;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.references-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
    background: #2c3e50;
    padding: 3rem;
    border-radius: 12px;
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: #ecf0f1;
}

.references-cta {
    text-align: center;
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 12px;
}

.references-cta h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.references-cta p {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin-bottom: 2rem;
}

/* Mobile Responsive Updates */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .about-features {
        grid-template-columns: 1fr;
    }

    .about-feature {
        flex-direction: column;
        text-align: center;
    }

    .references-grid {
        grid-template-columns: 1fr;
    }

    .references-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .success-features {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }
}

/* Info Pages Styles */
.info-content {
    padding: 4rem 0;
}

.info-text {
    max-width: 900px;
    margin: 0 auto;
}

.info-text h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.info-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 2rem 0 1rem;
    color: #2c3e50;
}

.info-text h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.info-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #555;
    text-align: justify;
}

.info-text ul {
    margin-bottom: 2rem;
    padding-left: 2rem;
}

.info-text li {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 0.5rem;
    color: #555;
}

.info-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.info-section {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid #e74c3c;
}

.info-section h4 {
    color: #e74c3c;
    margin-bottom: 1rem;
}

.info-section p {
    margin-bottom: 0;
    text-align: left;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.benefit-item {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
}

.benefit-item h4 {
    color: #e74c3c;
    margin-bottom: 1rem;
}

.benefit-item p {
    margin-bottom: 0;
    text-align: center;
}

.cta-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem;
    border-radius: 12px;
    text-align: center;
    margin-top: 3rem;
}

.cta-box h3 {
    color: white;
    margin-bottom: 1rem;
}

.cta-box p {
    color: rgba(255,255,255,0.9);
    margin-bottom: 2rem;
    text-align: center;
}

/* Breadcrumb Navigation */
.breadcrumb {
    background: #f8f9fa;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.breadcrumb-list {
    display: flex;
    list-style: none;
    gap: 0.5rem;
    align-items: center;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #e74c3c;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: 0.5rem;
    color: #6c757d;
}

.breadcrumb-item:last-child {
    color: #2c3e50;
    font-weight: 500;
}

/* Table Styles for Info Pages */
.info-table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.info-table th,
.info-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.info-table th {
    background: #2c3e50;
    color: white;
    font-weight: 600;
}

.info-table tr:hover {
    background: #f8f9fa;
}

/* Responsive for Info Pages */
@media (max-width: 768px) {
    .info-sections {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .info-table {
        font-size: 0.9rem;
    }

    .info-table th,
    .info-table td {
        padding: 0.75rem 0.5rem;
    }

    .breadcrumb-list {
        flex-wrap: wrap;
    }
}
