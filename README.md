# Atletizm Pisti Website Klonu

Bu proje, https://www.atletizmpisti.com.tr web sitesinin PHP ile yapılmış tam bir klonudur.

## Özellikler

- ✅ Responsive tasarım (mobil uyumlu)
- ✅ Modern PHP yapısı
- ✅ Temiz ve SEO dostu URL'ler
- ✅ İletişim formu (PHP ile)
- ✅ Mobil menü sistemi
- ✅ Smooth scrolling ve animasyonlar
- ✅ WhatsApp ve telefon entegrasyonu
- ✅ Lightbox galeri sistemi
- ✅ Form auto-save özelliği

## Dosya Yapısı

```
/
├── index.php                 # Ana sayfa
├── hakkimizda.php           # Hakkımızda sayfası
├── iletisim.php             # İletişim sayfası
├── referanslar.php          # Referanslar sayfası
├── includes/
│   ├── config.php           # Konfigürasyon dosyası
│   ├── header.php           # Header bileşeni
│   └── footer.php           # Footer bileşeni
├── bilgiler/                # B<PERSON>gi sayfaları klasörü
│   └── atletizm-pisti-nedir.php
├── assets/
│   ├── css/
│   │   └── style.css        # Ana CSS dosyası
│   ├── js/
│   │   └── script.js        # JavaScript dosyası
│   └── images/              # Resim dosyaları
└── README.md
```

## Kurulum

### 1. Gereksinimler
- PHP 7.4 veya üzeri
- Web sunucusu (Apache/Nginx)
- Modern web tarayıcısı

### 2. Kurulum Adımları

1. Dosyaları web sunucunuzun root dizinine kopyalayın
2. `assets/images/` klasörüne gerekli resim dosyalarını ekleyin
3. Web tarayıcınızda siteyi açın

### 3. Resim Dosyaları

Aşağıdaki resim dosyalarını `assets/images/` klasörüne eklemeniz gerekiyor:

**Logo:**
- `logo.png` - Ana logo dosyası

**Ana Sayfa:**
- `atletizm-pisti-hero.jpg` - Hero bölümü ana resmi
- `atletizm-pisti-yapimi.jpg` - Şirket bilgi bölümü resmi
- `atletizm-pisti-1.jpg` - Galeri resmi 1
- `atletizm-pisti-2.jpg` - Galeri resmi 2
- `atletizm-pisti-3.jpg` - Galeri resmi 3
- `atletizm-pisti-4.jpg` - Galeri resmi 4
- `atletizm-pisti-5.jpg` - Galeri resmi 5

**Referanslar:**
- `referans-1.jpg` - `referans-8.jpg` (8 adet referans resmi)

## Konfigürasyon

`includes/config.php` dosyasında aşağıdaki ayarları yapabilirsiniz:

- Site başlığı
- Şirket bilgileri
- İletişim bilgileri
- Menü yapısı

## Özelleştirme

### CSS Değişiklikleri
Ana stil dosyası `assets/css/style.css` içerisindedir. Renk şeması ve tasarım öğeleri buradan değiştirilebilir.

### JavaScript Özellikleri
`assets/js/script.js` dosyasında şu özellikler bulunur:
- Mobil menü toggle
- Smooth scrolling
- Form validasyonu
- Lightbox galeri
- Scroll animasyonları

## Sayfalar

### Ana Sayfa (index.php)
- Hero bölümü
- Hakkımızda özeti
- Özellikler grid'i
- Şirket bilgileri
- Galeri
- CTA bölümü

### Hakkımızda (hakkimizda.php)
- Şirket hikayesi
- Deneyim bilgileri
- Değerler

### İletişim (iletisim.php)
- İletişim bilgileri
- Çalışan iletişim formu
- Form validasyonu

### Referanslar (referanslar.php)
- Proje galerisi
- İstatistikler
- CTA bölümü

### Bilgiler Sayfaları
- `bilgiler/atletizm-pisti-nedir.php` - Örnek bilgi sayfası
- Diğer bilgi sayfaları benzer yapıda oluşturulabilir

## Responsive Tasarım

Site tamamen responsive olarak tasarlanmıştır:
- Desktop: 1200px+
- Tablet: 768px - 1199px
- Mobil: 767px ve altı

## SEO Özellikleri

- Semantic HTML5 yapısı
- Meta etiketleri
- Alt etiketleri
- Temiz URL yapısı
- Hızlı yükleme

## Tarayıcı Desteği

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Güvenlik

- Form validasyonu
- XSS koruması
- SQL injection koruması (veritabanı kullanılırsa)

## Performans

- Optimize edilmiş CSS ve JavaScript
- Lazy loading resimler
- Minimal HTTP istekleri

## Lisans

Bu proje eğitim amaçlı oluşturulmuştur. Ticari kullanım için gerekli izinleri alınız.

## Destek

Herhangi bir sorun yaşarsanız, lütfen issue açın veya iletişime geçin.

## Güncellemeler

- v1.0.0 - İlk sürüm (Tam klon)
- Gelecek güncellemeler için takipte kalın

## Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun
3. Değişikliklerinizi commit edin
4. Pull request gönderin

## Teşekkürler

Orijinal tasarım: https://www.atletizmpisti.com.tr
Web Tasarım & SEO: mira.net.tr
