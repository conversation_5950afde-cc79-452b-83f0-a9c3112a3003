<?php 
include 'includes/header.php';

// Handle form submission
$message_sent = false;
$error_message = '';

if ($_POST) {
    $name = trim($_POST['name'] ?? '');
    $surname = trim($_POST['surname'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // Basic validation
    if (empty($name) || empty($email) || empty($message)) {
        $error_message = 'Lütfen zorunlu alanları doldurunuz.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Geçerli bir e-posta adresi giriniz.';
    } else {
        // Here you would typically send the email
        // For demo purposes, we'll just set success message
        $message_sent = true;
    }
}
?>

<section class="page-header">
    <div class="container">
        <h1><PERSON><PERSON><PERSON><PERSON><PERSON></h1>
    </div>
</section>

<section class="contact-section">
    <div class="container">
        <div class="contact-grid">
            <div class="contact-info">
                <div class="contact-item">
                    <h3>Türkiye / İstanbul</h3>
                    <p><?php echo COMPANY_ADDRESS; ?></p>
                </div>
                
                <div class="contact-item">
                    <h3>Beograd / Srbija</h3>
                    <p>Dunavska 27B, Stari Grad, Beograd / SRBIJA</p>
                </div>
                
                <div class="contact-item">
                    <h3>Bizi Arayabilirsiniz</h3>
                    <p><a href="tel:<?php echo str_replace(' ', '', COMPANY_PHONE); ?>"><?php echo COMPANY_PHONE; ?></a></p>
                </div>
                
                <div class="contact-item">
                    <h3>Bizi Arayabilirsiniz</h3>
                    <p><a href="tel:+381643636993">+38 164 363 69 93</a></p>
                </div>
                
                <div class="contact-item">
                    <h3>E-Posta ile ulaşabilirsiniz</h3>
                    <p>
                        <a href="mailto:<?php echo COMPANY_EMAIL; ?>"><?php echo COMPANY_EMAIL; ?></a> & 
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                </div>
                
                <div class="contact-item">
                    <h3>E-Posta ile ulaşabilirsiniz</h3>
                    <p>
                        <a href="mailto:<EMAIL>"><EMAIL></a> & 
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                </div>
                
                <div class="contact-item">
                    <h3>WhatsApp üzerinden ulaşabilirsiniz.</h3>
                    <p><a href="https://wa.me/<?php echo COMPANY_WHATSAPP; ?>" target="_blank">0533 504 10 20</a></p>
                </div>
                
                <div class="contact-item">
                    <h3>Website</h3>
                    <p><a href="http://www.bilgiliinsaat.com" target="_blank">www.bilgiliinsaat.com</a></p>
                </div>
                
                <div class="contact-item">
                    <h3>Website</h3>
                    <p><a href="http://www.bilgilisports.com" target="_blank">www.bilgilisports.com</a></p>
                </div>
            </div>
            
            <div class="contact-form">
                <?php if ($message_sent): ?>
                    <div class="success-message">
                        <p>Mesajınız başarıyla gönderildi. En kısa sürede size dönüş yapacağız.</p>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="error-message">
                        <p><?php echo $error_message; ?></p>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Adınız</label>
                            <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="surname">Soyadınız</label>
                            <input type="text" id="surname" name="surname" value="<?php echo htmlspecialchars($_POST['surname'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">E-posta Adresi *</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Telefon Numarası</label>
                        <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="subject">Konu</label>
                        <input type="text" id="subject" name="subject" value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>" maxlength="180">
                        <small>0 / 180</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Mesaj</label>
                        <textarea id="message" name="message" rows="5" required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Mesaj Gönder</button>
                </form>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
